<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Daak Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/site.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" href="#" onclick="showDashboard()">Daak Management</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" href="#" onclick="showDashboard()">Daak Management</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="container-fluid">
        <main role="main" class="pb-3" id="mainContent">
            <!-- Dashboard content will be loaded here -->
        </main>
    </div>

    <!-- Create Demand Modal -->
    <div class="modal fade" id="createDemandModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle"></i> Create New Demand
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createDemandForm">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="demandDate" class="form-label">Date</label>
                                <input type="date" id="demandDate" class="form-control" required />
                            </div>
                            <div class="col-md-6">
                                <label for="senderDept" class="form-label">Sender Department</label>
                                <select id="senderDept" class="form-select" disabled>
                                    <option value="2">IT Department</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="receiverDept" class="form-label">Receiver Department</label>
                                <select id="receiverDept" class="form-select" required>
                                    <option value="">Select Receiver Department</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="demandSubject" class="form-label">Subject</label>
                                <input type="text" id="demandSubject" class="form-control" placeholder="Enter demand subject" required />
                            </div>
                            <div class="col-12">
                                <label for="demandContent" class="form-label">Content</label>
                                <textarea id="demandContent" class="form-control" rows="6" 
                                          placeholder="Enter full demand notice content..." required maxlength="2000"></textarea>
                                <div class="form-text">Maximum 2000 characters</div>
                                <div id="charCounter" class="form-text text-end text-muted">0/2000 characters</div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> Create Demand
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>