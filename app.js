// Mock data
const departments = [
    { id: 1, name: "Human Resources" },
    { id: 2, name: "IT Department" },
    { id: 3, name: "Finance" },
    { id: 4, name: "Operations" },
    { id: 5, name: "Marketing" }
];

const currentDepartmentId = 2; // IT Department

let demands = [
    {
        id: 1,
        date: "2024-01-15",
        senderDepartmentId: 2,
        receiverDepartmentId: 1,
        subject: "New Employee Laptop Setup",
        content: "We need to setup laptops for 3 new employees joining next week. Please coordinate with IT for hardware procurement and software installation.",
        status: "Pending",
        createdAt: "2024-01-15T10:30:00",
        senderDepartment: "IT Department",
        receiverDepartment: "Human Resources",
        timeline: [
            { department: "IT Department", status: "Sent", date: "2024-01-15T10:30:00" },
            { department: "Human Resources", status: "Pending", date: null }
        ]
    },
    {
        id: 2,
        date: "2024-01-14",
        senderDepartmentId: 3,
        receiverDepartmentId: 2,
        subject: "Budget Approval for Software Licenses",
        content: "Request for budget approval for annual software licenses including Microsoft Office 365 and Adobe Creative Suite for the design team.",
        status: "Approved",
        createdAt: "2024-01-14T14:20:00",
        senderDepartment: "Finance",
        receiverDepartment: "IT Department",
        timeline: [
            { department: "Finance", status: "Sent", date: "2024-01-14T14:20:00" },
            { department: "IT Department", status: "Approved", date: "2024-01-14T16:45:00" }
        ]
    },
    {
        id: 3,
        date: "2024-01-13",
        senderDepartmentId: 2,
        receiverDepartmentId: 4,
        subject: "Server Maintenance Schedule",
        content: "Scheduled server maintenance on Saturday from 2 AM to 6 AM. All systems will be temporarily unavailable during this period.",
        status: "Forwarded",
        createdAt: "2024-01-13T09:15:00",
        senderDepartment: "IT Department",
        receiverDepartment: "Operations",
        timeline: [
            { department: "IT Department", status: "Sent", date: "2024-01-13T09:15:00" },
            { department: "Operations", status: "Forwarded", date: "2024-01-13T11:30:00" },
            { department: "Human Resources", status: "Pending", date: null }
        ]
    }
];

let nextDemandId = 4;

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    showDashboard();
    setupEventListeners();
});

function setupEventListeners() {
    // Create demand form
    document.getElementById('createDemandForm').addEventListener('submit', handleCreateDemand);
    
    // Character counter for content textarea
    const contentTextarea = document.getElementById('demandContent');
    contentTextarea.addEventListener('input', updateCharacterCounter);
    
    // Date input default value
    document.getElementById('demandDate').value = new Date().toISOString().split('T')[0];
}

function showDashboard() {
    const mainContent = document.getElementById('mainContent');
    mainContent.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-envelope-paper"></i> Daak Management System</h2>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs mb-4" id="demandTabs">
            <li class="nav-item">
                <button class="nav-link active" onclick="switchTab('demands')" id="demandsTab">
                    <i class="bi bi-send"></i> Demands
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" onclick="switchTab('received')" id="receivedTab">
                    <i class="bi bi-inbox"></i> Received Demands
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div id="tabContent">
            <!-- Content will be loaded here -->
        </div>
    `;

    loadDepartmentsInModal();
    switchTab('demands');
}

function switchTab(tab) {
    // Update tab appearance
    document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
    document.getElementById(tab + 'Tab').classList.add('active');

    // Load content
    if (tab === 'demands') {
        loadDemandsTab();
    } else {
        loadReceivedDemandsTab();
    }
}

function loadDemandsTab() {
    const tabContent = document.getElementById('tabContent');
    tabContent.innerHTML = `
        <!-- Create Demand Button -->
        <div class="d-flex justify-content-end mb-3">
            <button class="btn btn-primary" onclick="openCreateDemandModal()">
                <i class="bi bi-plus-circle"></i> Create Demand
            </button>
        </div>

        <!-- Filters -->
        <div class="filter-section mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="demandsStartDate" class="form-label">Start Date</label>
                    <input type="date" id="demandsStartDate" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="demandsEndDate" class="form-label">End Date</label>
                    <input type="date" id="demandsEndDate" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="demandsStatus" class="form-label">Status</label>
                    <select id="demandsStatus" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-outline-primary" onclick="filterDemands()">
                        <i class="bi bi-funnel"></i> View
                    </button>
                </div>
            </div>
        </div>

        <!-- Demands Table -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Date</th>
                        <th>Receiver Department</th>
                        <th>Subject</th>
                        <th>Status (Action)</th>
                    </tr>
                </thead>
                <tbody id="demandsTableBody">
                    <!-- Table rows will be populated here -->
                </tbody>
            </table>
        </div>
    `;

    loadDemandsTable();
}

function loadReceivedDemandsTab() {
    const tabContent = document.getElementById('tabContent');
    tabContent.innerHTML = `
        <!-- Filters -->
        <div class="filter-section mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="receivedStartDate" class="form-label">Start Date</label>
                    <input type="date" id="receivedStartDate" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="receivedEndDate" class="form-label">End Date</label>
                    <input type="date" id="receivedEndDate" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="receivedStatus" class="form-label">Status</label>
                    <select id="receivedStatus" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-outline-primary" onclick="filterReceivedDemands()">
                        <i class="bi bi-funnel"></i> View
                    </button>
                </div>
            </div>
        </div>

        <!-- Received Demands Table -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Date</th>
                        <th>Sender Department</th>
                        <th>Subject</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="receivedDemandsTableBody">
                    <!-- Table rows will be populated here -->
                </tbody>
            </table>
        </div>
    `;

    loadReceivedDemandsTable();
}

function loadDemandsTable() {
    const sentDemands = demands.filter(d => d.senderDepartmentId === currentDepartmentId);
    const tableBody = document.getElementById('demandsTableBody');

    if (sentDemands.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-4">
                    <i class="bi bi-inbox display-6 text-muted"></i>
                    <p class="text-muted mt-2">No demands found</p>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = sentDemands.map(demand => `
        <tr>
            <td>${formatDate(demand.date)}</td>
            <td>${demand.receiverDepartment}</td>
            <td>${demand.subject}</td>
            <td>
                <span class="badge status-badge status-${demand.status.toLowerCase()} me-2">${demand.status}</span>
                <button class="btn btn-outline-primary btn-sm" onclick="viewDemandTimeline(${demand.id})">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function loadReceivedDemandsTable() {
    const receivedDemands = demands.filter(d => d.receiverDepartmentId === currentDepartmentId);
    const tableBody = document.getElementById('receivedDemandsTableBody');

    if (receivedDemands.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-4">
                    <i class="bi bi-inbox display-6 text-muted"></i>
                    <p class="text-muted mt-2">No received demands found</p>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = receivedDemands.map(demand => `
        <tr>
            <td>${formatDate(demand.date)}</td>
            <td>${demand.senderDepartment}</td>
            <td>${demand.subject}</td>
            <td>
                <button class="btn btn-outline-primary btn-sm" onclick="viewReceivedDemandDetails(${demand.id})" title="View Details">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function filterDemands() {
    loadDemandsTable();
}

function filterReceivedDemands() {
    loadReceivedDemandsTable();
}

function openCreateDemandModal() {
    const modal = new bootstrap.Modal(document.getElementById('createDemandModal'));
    modal.show();
}

function loadDepartmentsInModal() {
    const receiverSelect = document.getElementById('receiverDept');
    receiverSelect.innerHTML = '<option value="">Select Receiver Department</option>';
    
    departments.filter(dept => dept.id !== currentDepartmentId).forEach(dept => {
        receiverSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
    });
}

function handleCreateDemand(e) {
    e.preventDefault();

    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;

    // Show loading state
    submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating...';
    submitButton.disabled = true;

    // Simulate API call
    setTimeout(() => {
        const receiverDeptId = parseInt(document.getElementById('receiverDept').value);
        const newDemand = {
            id: nextDemandId++,
            date: document.getElementById('demandDate').value,
            senderDepartmentId: currentDepartmentId,
            receiverDepartmentId: receiverDeptId,
            subject: document.getElementById('demandSubject').value,
            content: document.getElementById('demandContent').value,
            status: "Pending",
            createdAt: new Date().toISOString(),
            senderDepartment: departments.find(d => d.id === currentDepartmentId).name,
            receiverDepartment: departments.find(d => d.id === receiverDeptId).name,
            timeline: [
                { department: departments.find(d => d.id === currentDepartmentId).name, status: "Sent", date: new Date().toISOString() },
                { department: departments.find(d => d.id === receiverDeptId).name, status: "Pending", date: null }
            ]
        };

        demands.push(newDemand);

        // Close modal and refresh
        const modal = bootstrap.Modal.getInstance(document.getElementById('createDemandModal'));
        modal.hide();

        // Reset form
        e.target.reset();
        document.getElementById('demandDate').value = new Date().toISOString().split('T')[0];
        updateCharacterCounter();

        // Show success message
        showAlert('Demand created successfully!', 'success');

        // Refresh current tab
        const activeTab = document.querySelector('.nav-link.active').id;
        if (activeTab === 'demandsTab') {
            loadDemandsTable();
        } else {
            loadReceivedDemandsTable();
        }

        // Restore button state
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }, 1000);
}

function processDemand(demandId, newStatus) {
    const demand = demands.find(d => d.id === demandId);
    if (demand) {
        demand.status = newStatus;
        showAlert(`Demand ${newStatus.toLowerCase()} successfully!`, 'success');
        loadReceivedDemandsTable();
    }
}

function viewDemandTimeline(demandId) {
    const demand = demands.find(d => d.id === demandId);
    if (!demand) return;

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history"></i> Demand Timeline
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6 class="mb-3">${demand.subject}</h6>
                    <div class="timeline">
                        ${demand.timeline.map((step, index) => `
                            <div class="timeline-item ${step.status === 'Pending' ? 'pending' : 'completed'}">
                                <div class="timeline-marker">
                                    <i class="bi ${getTimelineIcon(step.status)}"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6>${step.department}</h6>
                                    <span class="badge status-badge status-${step.status.toLowerCase()}">${step.status}</span>
                                    ${step.date ? `<p class="text-muted mb-0"><small>${formatDateTime(step.date)}</small></p>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function viewReceivedDemandDetails(demandId) {
    const demand = demands.find(d => d.id === demandId);
    if (!demand) return;

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-file-text"></i> Details of Daak
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Date, From Department, To Department in one row -->
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <strong>Date:</strong><br>
                            <span class="text-muted">${formatDate(demand.date)}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>From Department:</strong><br>
                            <span class="text-muted">${demand.senderDepartment}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>To Department:</strong><br>
                            <span class="text-muted">${demand.receiverDepartment}</span>
                        </div>
                    </div>

                    <!-- Subject -->
                    <div class="mb-3">
                        <strong>Subject:</strong><br>
                        <span class="text-muted">${demand.subject}</span>
                    </div>

                    <!-- Uneditable TextArea with demand content -->
                    <div class="mb-3">
                        <strong>Demand Content:</strong><br>
                        <textarea class="form-control" rows="6" readonly style="background-color: #f8f9fa;">${demand.content}</textarea>
                    </div>

                    ${demand.status === 'Pending' ? `
                    <!-- Comment box and Forward dropdown in same row -->
                    <div class="row g-3 mb-3">
                        <div class="col-md-8">
                            <strong>Comment (in case of rejection):</strong><br>
                            <textarea id="rejectComment_${demand.id}" class="form-control" rows="3" placeholder="Enter rejection reason..."></textarea>
                        </div>
                        <div class="col-md-4">
                            <strong>Forward to Department:</strong><br>
                            <select id="forwardDept_${demand.id}" class="form-select mb-2">
                                <option value="">Select Department</option>
                                ${departments.filter(dept => dept.id !== currentDepartmentId && dept.id !== demand.senderDepartmentId).map(dept =>
                                    `<option value="${dept.id}">${dept.name}</option>`
                                ).join('')}
                            </select>
                            <button type="button" class="btn btn-warning btn-sm w-100" onclick="forwardDemand(${demand.id})">
                                <i class="bi bi-arrow-right-circle"></i> Forward
                            </button>
                        </div>
                    </div>

                    <!-- Approve and Reject buttons -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-success w-100" onclick="approveDemand(${demand.id})">
                                <i class="bi bi-check-circle"></i> Approve
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-danger w-100" onclick="rejectDemand(${demand.id})">
                                <i class="bi bi-x-circle"></i> Reject
                            </button>
                        </div>
                    </div>
                    ` : `
                    <!-- Show status for non-pending demands -->
                    <div class="alert alert-info">
                        <strong>Status:</strong>
                        <span class="badge status-badge status-${demand.status.toLowerCase()} ms-2">${demand.status}</span>
                    </div>
                    `}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function getTimelineIcon(status) {
    switch(status) {
        case 'Sent': return 'bi-send';
        case 'Pending': return 'bi-clock';
        case 'Approved': return 'bi-check-circle';
        case 'Rejected': return 'bi-x-circle';
        case 'Forwarded': return 'bi-arrow-right-circle';
        default: return 'bi-circle';
    }
}

function approveDemand(demandId) {
    const demand = demands.find(d => d.id === demandId);
    if (demand) {
        demand.status = 'Approved';
        // Update timeline
        const lastStep = demand.timeline[demand.timeline.length - 1];
        if (lastStep.status === 'Pending') {
            lastStep.status = 'Approved';
            lastStep.date = new Date().toISOString();
        }

        showAlert('Demand approved successfully!', 'success');
        loadReceivedDemandsTable();

        // Close modal
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }
    }
}

function rejectDemand(demandId) {
    const demand = demands.find(d => d.id === demandId);
    if (demand) {
        demand.status = 'Rejected';
        // Update timeline
        const lastStep = demand.timeline[demand.timeline.length - 1];
        if (lastStep.status === 'Pending') {
            lastStep.status = 'Rejected';
            lastStep.date = new Date().toISOString();
        }

        showAlert('Demand rejected successfully!', 'success');
        loadReceivedDemandsTable();

        // Close modal
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }
    }
}

function forwardDemand(demandId) {
    const forwardDeptId = parseInt(document.getElementById(`forwardDept_${demandId}`).value);
    if (!forwardDeptId) {
        showAlert('Please select a department to forward to!', 'warning');
        return;
    }

    const demand = demands.find(d => d.id === demandId);
    if (demand) {
        demand.status = 'Forwarded';
        demand.receiverDepartmentId = forwardDeptId;
        demand.receiverDepartment = departments.find(d => d.id === forwardDeptId).name;

        // Update timeline
        const lastStep = demand.timeline[demand.timeline.length - 1];
        if (lastStep.status === 'Pending') {
            lastStep.status = 'Forwarded';
            lastStep.date = new Date().toISOString();
        }

        // Add new pending step
        demand.timeline.push({
            department: departments.find(d => d.id === forwardDeptId).name,
            status: 'Pending',
            date: null
        });

        showAlert('Demand forwarded successfully!', 'success');
        loadReceivedDemandsTable();

        // Close modal
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }
    }
}

function updateCharacterCounter() {
    const textarea = document.getElementById('demandContent');
    const counter = document.getElementById('charCounter');
    const length = textarea.value.length;
    const maxLength = 2000;

    counter.textContent = `${length}/${maxLength} characters`;

    if (length > maxLength * 0.9) {
        counter.className = 'form-text text-end text-warning';
    } else {
        counter.className = 'form-text text-end text-muted';
    }
}



function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
}