// Mock data
const departments = [
    { id: 1, name: "Human Resources" },
    { id: 2, name: "IT Department" },
    { id: 3, name: "Finance" },
    { id: 4, name: "Operations" },
    { id: 5, name: "Marketing" }
];

const currentDepartmentId = 2; // IT Department

let demands = [
    {
        id: 1,
        date: "2024-01-15",
        senderDepartmentId: 2,
        receiverDepartmentId: 1,
        subject: "New Employee Laptop Setup",
        content: "We need to setup laptops for 3 new employees joining next week. Please coordinate with IT for hardware procurement and software installation.",
        status: "Pending",
        createdAt: "2024-01-15T10:30:00",
        senderDepartment: "IT Department",
        receiverDepartment: "Human Resources"
    },
    {
        id: 2,
        date: "2024-01-14",
        senderDepartmentId: 3,
        receiverDepartmentId: 2,
        subject: "Budget Approval for Software Licenses",
        content: "Request for budget approval for annual software licenses including Microsoft Office 365 and Adobe Creative Suite for the design team.",
        status: "Approved",
        createdAt: "2024-01-14T14:20:00",
        senderDepartment: "Finance",
        receiverDepartment: "IT Department"
    },
    {
        id: 3,
        date: "2024-01-13",
        senderDepartmentId: 2,
        receiverDepartmentId: 4,
        subject: "Server Maintenance Schedule",
        content: "Scheduled server maintenance on Saturday from 2 AM to 6 AM. All systems will be temporarily unavailable during this period.",
        status: "Forwarded",
        createdAt: "2024-01-13T09:15:00",
        senderDepartment: "IT Department",
        receiverDepartment: "Operations"
    }
];

let nextDemandId = 4;

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    showDashboard();
    setupEventListeners();
});

function setupEventListeners() {
    // Create demand form
    document.getElementById('createDemandForm').addEventListener('submit', handleCreateDemand);
    
    // Character counter for content textarea
    const contentTextarea = document.getElementById('demandContent');
    contentTextarea.addEventListener('input', updateCharacterCounter);
    
    // Date input default value
    document.getElementById('demandDate').value = new Date().toISOString().split('T')[0];
}

function showDashboard() {
    const mainContent = document.getElementById('mainContent');
    mainContent.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-envelope-paper"></i> Daak Management Dashboard</h2>
            <button class="btn btn-primary" onclick="openCreateDemandModal()">
                <i class="bi bi-plus-circle"></i> Create New Demand
            </button>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="filterStatus" class="form-label">Status</label>
                    <select id="filterStatus" class="form-select" onchange="applyFilters()">
                        <option value="">All Statuses</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                        <option value="Forwarded">Forwarded</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filterDepartment" class="form-label">Department</label>
                    <select id="filterDepartment" class="form-select" onchange="applyFilters()">
                        <option value="">All Departments</option>
                        ${departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('')}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filterDateFrom" class="form-label">From Date</label>
                    <input type="date" id="filterDateFrom" class="form-control" onchange="applyFilters()">
                </div>
                <div class="col-md-3">
                    <label for="filterDateTo" class="form-label">To Date</label>
                    <input type="date" id="filterDateTo" class="form-control" onchange="applyFilters()">
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs mb-4" id="demandTabs">
            <li class="nav-item">
                <button class="nav-link active" onclick="switchTab('sent')" id="sentTab">
                    <i class="bi bi-send"></i> Sent Demands
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" onclick="switchTab('received')" id="receivedTab">
                    <i class="bi bi-inbox"></i> Received Demands
                </button>
            </li>
        </ul>

        <!-- Demands Content -->
        <div id="demandsContent">
            <!-- Content will be loaded here -->
        </div>
    `;
    
    loadDepartmentsInModal();
    switchTab('sent');
}

function switchTab(tab) {
    // Update tab appearance
    document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
    document.getElementById(tab + 'Tab').classList.add('active');
    
    // Load content
    if (tab === 'sent') {
        loadSentDemands();
    } else {
        loadReceivedDemands();
    }
}

function loadSentDemands() {
    const sentDemands = demands.filter(d => d.senderDepartmentId === currentDepartmentId);
    displayDemands(sentDemands, 'sent');
}

function loadReceivedDemands() {
    const receivedDemands = demands.filter(d => d.receiverDepartmentId === currentDepartmentId);
    displayDemands(receivedDemands, 'received');
}

function displayDemands(demandsToShow, type) {
    const content = document.getElementById('demandsContent');
    
    if (demandsToShow.length === 0) {
        content.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="text-muted mt-3">No ${type} demands found</h4>
                <p class="text-muted">There are no demands to display at the moment.</p>
            </div>
        `;
        return;
    }

    const demandsHtml = demandsToShow.map(demand => `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card demand-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="badge status-badge status-${demand.status.toLowerCase()}">${demand.status}</span>
                    <small class="text-muted">${formatDate(demand.date)}</small>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${demand.subject}</h6>
                    <p class="card-text text-truncate-2">${demand.content}</p>
                    <div class="demand-meta mt-2">
                        <div class="mb-1">
                            <i class="bi bi-building"></i>
                            <span class="department-badge">${type === 'sent' ? demand.receiverDepartment : demand.senderDepartment}</span>
                        </div>
                        <small class="text-muted">
                            <i class="bi bi-clock"></i> ${formatDateTime(demand.createdAt)}
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-outline-primary btn-sm" onclick="viewDemandDetails(${demand.id})">
                        <i class="bi bi-eye"></i> View Details
                    </button>
                    ${type === 'received' && demand.status === 'Pending' ? `
                        <button class="btn btn-outline-success btn-sm ms-1" onclick="processDemand(${demand.id}, 'Approved')">
                            <i class="bi bi-check-circle"></i> Approve
                        </button>
                        <button class="btn btn-outline-danger btn-sm ms-1" onclick="processDemand(${demand.id}, 'Rejected')">
                            <i class="bi bi-x-circle"></i> Reject
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');

    content.innerHTML = `<div class="row">${demandsHtml}</div>`;
}

function openCreateDemandModal() {
    const modal = new bootstrap.Modal(document.getElementById('createDemandModal'));
    modal.show();
}

function loadDepartmentsInModal() {
    const receiverSelect = document.getElementById('receiverDept');
    receiverSelect.innerHTML = '<option value="">Select Receiver Department</option>';
    
    departments.filter(dept => dept.id !== currentDepartmentId).forEach(dept => {
        receiverSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
    });
}

function handleCreateDemand(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        const newDemand = {
            id: nextDemandId++,
            date: document.getElementById('demandDate').value,
            senderDepartmentId: currentDepartmentId,
            receiverDepartmentId: parseInt(document.getElementById('receiverDept').value),
            subject: document.getElementById('demandSubject').value,
            content: document.getElementById('demandContent').value,
            status: "Pending",
            createdAt: new Date().toISOString(),
            senderDepartment: departments.find(d => d.id === currentDepartmentId).name,
            receiverDepartment: departments.find(d => d.id === parseInt(document.getElementById('receiverDept').value)).name
        };
        
        demands.push(newDemand);
        
        // Close modal and refresh
        const modal = bootstrap.Modal.getInstance(document.getElementById('createDemandModal'));
        modal.hide();
        
        // Reset form
        e.target.reset();
        document.getElementById('demandDate').value = new Date().toISOString().split('T')[0];
        updateCharacterCounter();
        
        // Show success message
        showAlert('Demand created successfully!', 'success');
        
        // Refresh current tab
        const activeTab = document.querySelector('.nav-link.active').id;
        if (activeTab === 'sentTab') {
            loadSentDemands();
        } else {
            loadReceivedDemands();
        }
        
        // Restore button state
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }, 1000);
}

function processDemand(demandId, newStatus) {
    const demand = demands.find(d => d.id === demandId);
    if (demand) {
        demand.status = newStatus;
        showAlert(`Demand ${newStatus.toLowerCase()} successfully!`, 'success');
        loadReceivedDemands();
    }
}

function viewDemandDetails(demandId) {
    const demand = demands.find(d => d.id === demandId);
    if (!demand) return;
    
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-file-text"></i> Demand Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <strong>Date:</strong> ${formatDate(demand.date)}
                        </div>
                        <div class="col-md-6">
                            <strong>Status:</strong> 
                            <span class="badge status-badge status-${demand.status.toLowerCase()}">${demand.status}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>From:</strong> ${demand.senderDepartment}
                        </div>
                        <div class="col-md-6">
                            <strong>To:</strong> ${demand.receiverDepartment}
                        </div>
                        <div class="col-12">
                            <strong>Subject:</strong><br>
                            ${demand.subject}
                        </div>
                        <div class="col-12">
                            <strong>Content:</strong><br>
                            <div class="border p-3 bg-light rounded">${demand.content}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">
                                <i class="bi bi-clock"></i> Created: ${formatDateTime(demand.createdAt)}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function updateCharacterCounter() {
    const textarea = document.getElementById('demandContent');
    const counter = document.getElementById('charCounter');
    const length = textarea.value.length;
    const maxLength = 2000;
    
    counter.textContent = `${length}/${maxLength} characters`;
    
    if (length > maxLength * 0.9) {
        counter.className = 'form-text text-end text-warning';
    } else {
        counter.className = 'form-text text-end text-muted';
    }
}

function applyFilters() {
    // Get filter values
    const statusFilter = document.getElementById('filterStatus').value;
    const departmentFilter = document.getElementById('filterDepartment').value;
    const dateFromFilter = document.getElementById('filterDateFrom').value;
    const dateToFilter = document.getElementById('filterDateTo').value;
    
    // Apply filters and refresh current tab
    const activeTab = document.querySelector('.nav-link.active').id;
    if (activeTab === 'sentTab') {
        loadSentDemands();
    } else {
        loadReceivedDemands();
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
}